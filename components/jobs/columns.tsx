"use client";

import type { ColumnDef } from "@tanstack/react-table";
import {
  AlertCircle,
  ArrowUpDown,
  Building2,
  Calendar,
  CheckCircle2,
  Clock,
  DollarSign,
  ExternalLink,
  Eye,
  Globe,
  Hash,
  Link2,
  Mail,
  MapPin,
  MoreHorizontal,
  Star,
  XCircle,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import type { DatabaseJob } from "@/lib/storage";

// Regex constants for performance
const URL_PROTOCOL_REGEX = /^https?:\/\//;

// Helper function to truncate long text with tooltip
const TruncatedText = ({
  text,
  maxLength = 50,
}: {
  text: string;
  maxLength?: number;
}) => {
  if (!text) {
    return <div className="text-muted-foreground">-</div>;
  }

  if (text.length <= maxLength) {
    return <div className="text-sm">{text}</div>;
  }

  return (
    <div className="text-sm" title={text}>
      {text.slice(0, maxLength)}...
    </div>
  );
};

// biome-ignore lint/suspicious/noExplicitAny: JSON field values can be any structure (string, object, array, etc.)
const formatJsonField = (value: any) => {
  if (!value) {
    return "-";
  }
  if (typeof value === "object") {
    return `${JSON.stringify(value).slice(0, 100)}...`;
  }
  return String(value);
};

export const columns: ColumnDef<DatabaseJob>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        aria-label="Select all"
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value: boolean) =>
          table.toggleAllPageRowsSelected(!!value)
        }
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        aria-label="Select row"
        checked={row.getIsSelected()}
        onCheckedChange={(value: boolean) => row.toggleSelected(!!value)}
        onClick={(e) => e.stopPropagation()}
      />
    ),
    enableSorting: false,
    enableHiding: false,
    meta: {
      className: "sticky left-0 z-10 bg-background shadow-sm min-w-[50px]",
    },
  },

  // === CORE IDENTIFIERS ===
  {
    accessorKey: "id",
    header: ({ column }) => {
      return (
        <Button
          className="h-8 px-2"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          variant="ghost"
        >
          <Hash className="mr-2 h-4 w-4" />
          ID
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const id = row.getValue("id") as string;
      return (
        <div className="font-mono text-xs" title={id}>
          {id.slice(0, 8)}...
        </div>
      );
    },
    meta: {
      className:
        "sticky left-[50px] z-10 bg-background shadow-sm min-w-[120px]",
    },
  },

  // === CORE JOB INFORMATION ===
  {
    accessorKey: "title",
    header: ({ column }) => {
      return (
        <Button
          className="h-8 px-2"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          variant="ghost"
        >
          Title
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const title = row.getValue("title") as string;
      const jobId = row.original.id;

      return (
        <div className="w-[200px]">
          <a
            className="block cursor-pointer font-medium text-blue-600 text-xs hover:text-blue-800 hover:underline leading-tight whitespace-normal break-words"
            href={`/dashboard/jobs/${jobId}`}
            onClick={(e) => e.stopPropagation()}
            title={title}
          >
            {title}
          </a>
        </div>
      );
    },
    meta: {
      className:
        "sticky left-[170px] z-10 bg-background shadow-sm min-w-[300px]",
    },
  },
  {
    accessorKey: "company",
    header: ({ column }) => {
      return (
        <Button
          className="h-8 px-2"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          variant="ghost"
        >
          <Building2 className="mr-2 h-4 w-4" />
          Company
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const company = row.getValue("company") as string;
      return <div className="font-medium">{company || "Not specified"}</div>;
    },
  },
  {
    accessorKey: "description",
    header: "Description",
    cell: ({ row }) => {
      const description = row.getValue("description") as string;
      return <TruncatedText maxLength={100} text={description} />;
    },
  },
  {
    accessorKey: "department",
    header: "Department",
    cell: ({ row }) => {
      const department = row.getValue("department") as string;
      return <div className="text-sm">{department || "-"}</div>;
    },
  },
  {
    accessorKey: "type",
    header: "Type",
    cell: ({ row }) => {
      const type = row.getValue("type") as string;
      return <div className="capitalize">{type || "Not specified"}</div>;
    },
  },
  {
    accessorKey: "industry",
    header: "Industry",
    cell: ({ row }) => {
      const industry = row.getValue("industry") as string;
      return <div className="text-sm">{industry || "-"}</div>;
    },
  },
  {
    accessorKey: "occupational_category",
    header: "Category",
    cell: ({ row }) => {
      const category = row.getValue("occupational_category") as string;
      return <div className="text-sm">{category || "-"}</div>;
    },
  },

  // === APPLICATION & URLS ===
  {
    accessorKey: "apply_url",
    header: "Apply URL",
    cell: ({ row }) => {
      const url = row.getValue("apply_url") as string;
      if (!url) {
        return <div className="text-muted-foreground">-</div>;
      }

      return (
        <div className="flex items-center">
          <Link2 className="mr-2 h-4 w-4 text-muted-foreground" />
          <a
            className="max-w-[200px] truncate text-blue-600 hover:underline"
            href={url}
            onClick={(e) => e.stopPropagation()}
            rel="noopener noreferrer"
            target="_blank"
            title={url}
          >
            {url.replace(URL_PROTOCOL_REGEX, "").slice(0, 30)}...
          </a>
        </div>
      );
    },
  },
  {
    accessorKey: "apply_method",
    header: "Apply Method",
    cell: ({ row }) => {
      const method = row.getValue("apply_method") as string;
      const getMethodIcon = (methodVal: string) => {
        switch (methodVal) {
          case "email":
            return <Mail className="mr-1 size-3" />;
          case "form":
            return <ExternalLink className="mr-1 size-3" />;
          case "url":
            return <ExternalLink className="mr-1 size-3" />;
          default:
            return <ExternalLink className="mr-1 size-3" />;
        }
      };

      if (!method) {
        return <div className="text-muted-foreground">-</div>;
      }

      return (
        <Badge className="text-xs" variant="outline">
          {getMethodIcon(method)}
          {method}
        </Badge>
      );
    },
  },
  {
    accessorKey: "source_url",
    header: "Source URL",
    cell: ({ row }) => {
      const url = row.getValue("source_url") as string;
      if (!url) {
        return <div className="text-muted-foreground">-</div>;
      }

      return (
        <div className="flex items-center">
          <Globe className="mr-2 h-4 w-4 text-muted-foreground" />
          <a
            className="max-w-[200px] truncate text-blue-600 hover:underline"
            href={url}
            onClick={(e) => e.stopPropagation()}
            rel="noopener noreferrer"
            target="_blank"
            title={url}
          >
            {url.replace(URL_PROTOCOL_REGEX, "").slice(0, 30)}...
          </a>
        </div>
      );
    },
  },

  // === STATUS FIELDS ===
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.getValue("status") as string;
      const getStatusVariant = (statusVal: string) => {
        switch (statusVal) {
          case "active":
            return "default";
          case "closed":
            return "secondary";
          case "filled":
            return "destructive";
          case "expired":
            return "outline";
          default:
            return "outline";
        }
      };
      return (
        <Badge className="capitalize" variant={getStatusVariant(status)}>
          {status}
        </Badge>
      );
    },
  },
  {
    accessorKey: "processing_status",
    header: "Processing",
    cell: ({ row }) => {
      const status = row.getValue("processing_status") as string;
      const getProcessingVariant = (statusVal: string) => {
        switch (statusVal) {
          case "completed":
            return "default";
          case "pending":
            return "secondary";
          case "failed":
            return "destructive";
          default:
            return "outline";
        }
      };
      const getProcessingIcon = (statusVal: string) => {
        switch (statusVal) {
          case "completed":
            return <CheckCircle2 className="mr-1 size-3" />;
          case "pending":
            return <Clock className="mr-1 size-3" />;
          case "failed":
            return <XCircle className="mr-1 size-3" />;
          default:
            return <AlertCircle className="mr-1 size-3" />;
        }
      };
      return (
        <Badge className="capitalize" variant={getProcessingVariant(status)}>
          {getProcessingIcon(status)}
          {status || "unknown"}
        </Badge>
      );
    },
  },
  {
    accessorKey: "monitor_status",
    header: "Monitor",
    cell: ({ row }) => {
      const status = row.getValue("monitor_status") as string;
      if (!status) {
        return <div className="text-muted-foreground">-</div>;
      }
      return (
        <Badge className="capitalize" variant="outline">
          <Eye className="mr-1 h-3 w-3" />
          {status}
        </Badge>
      );
    },
  },
  {
    accessorKey: "featured",
    header: "Featured",
    cell: ({ row }) => {
      const featured = row.getValue("featured") as boolean;
      return (
        <div className="text-center">
          {featured ? (
            <Badge variant="default">
              <Star className="mr-1 h-3 w-3" />
              Featured
            </Badge>
          ) : (
            <div className="text-muted-foreground">-</div>
          )}
        </div>
      );
    },
  },

  // === WORKPLACE & LOCATION ===
  {
    accessorKey: "workplace_type",
    header: "Workplace",
    cell: ({ row }) => {
      const workplaceType = row.getValue("workplace_type") as string;
      const getWorkplaceIcon = (type: string) => {
        switch (type) {
          case "Remote":
            return "🏠";
          case "On-site":
            return "🏢";
          case "Hybrid":
            return "🔄";
          default:
            return "❓";
        }
      };
      return (
        <div className="flex items-center">
          <span className="mr-2">{getWorkplaceIcon(workplaceType)}</span>
          <span className="capitalize">{workplaceType || "Not specified"}</span>
        </div>
      );
    },
  },
  {
    accessorKey: "remote_region",
    header: "Remote Region",
    cell: ({ row }) => {
      const region = row.getValue("remote_region") as string;
      return <div className="text-sm">{region || "-"}</div>;
    },
  },
  {
    accessorKey: "workplace_city",
    header: ({ column }) => {
      return (
        <Button
          className="h-8 px-2"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          variant="ghost"
        >
          <MapPin className="mr-2 h-4 w-4" />
          City
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const city = row.getValue("workplace_city") as string;
      return <div className="text-sm">{city || "-"}</div>;
    },
  },
  {
    accessorKey: "workplace_country",
    header: ({ column }) => {
      return (
        <Button
          className="h-8 px-2"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          variant="ghost"
        >
          Country
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const country = row.getValue("workplace_country") as string;
      return <div className="text-sm">{country || "-"}</div>;
    },
  },
  {
    accessorKey: "timezone_requirements",
    header: "Timezone",
    cell: ({ row }) => {
      const timezone = row.getValue("timezone_requirements") as string;
      return <div className="text-sm">{timezone || "-"}</div>;
    },
  },
  {
    accessorKey: "travel_required",
    header: "Travel",
    cell: ({ row }) => {
      const travel = row.getValue("travel_required") as boolean;
      return (
        <div className="text-center">
          {(() => {
            if (travel === true) {
              return <Badge variant="outline">Required</Badge>;
            }
            if (travel === false) {
              return <Badge variant="secondary">None</Badge>;
            }
            return <div className="text-muted-foreground">-</div>;
          })()}
        </div>
      );
    },
  },

  // === SALARY & COMPENSATION ===
  {
    accessorKey: "salary_min",
    header: ({ column }) => {
      return (
        <Button
          className="h-8 px-2"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          variant="ghost"
        >
          <DollarSign className="mr-2 h-4 w-4" />
          Salary
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const salaryMin = row.getValue("salary_min") as number;
      const salaryMax = row.original.salary_max as number;
      const currency = row.original.salary_currency as string;
      const unit = row.original.salary_unit as string;

      if (!(salaryMin || salaryMax)) {
        return <div className="text-muted-foreground">-</div>;
      }

      const formatSalary = (amount: number) => {
        if (amount >= 1_000_000) {
          return `${(amount / 1_000_000).toFixed(1)}M`;
        }
        if (amount >= 1000) {
          return `${(amount / 1000).toFixed(0)}k`;
        }
        return amount.toLocaleString();
      };

      let salaryText = "";
      if (salaryMin && salaryMax) {
        salaryText = `${formatSalary(salaryMin)}-${formatSalary(salaryMax)}`;
      } else {
        salaryText = `${formatSalary(salaryMin || salaryMax)}`;
      }

      return (
        <div className="text-right">
          <div className="font-medium">
            {currency || "USD"}
            {salaryText}
          </div>
          {unit && <div className="text-muted-foreground text-xs">/{unit}</div>}
        </div>
      );
    },
  },
  {
    accessorKey: "salary_max",
    header: ({ column }) => {
      return (
        <Button
          className="h-8 px-2"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          variant="ghost"
        >
          Max Salary
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const salaryMax = row.getValue("salary_max") as number;
      const currency = row.original.salary_currency as string;

      if (!salaryMax) {
        return <div className="text-muted-foreground">-</div>;
      }

      const formatSalary = (amount: number) => {
        if (amount >= 1_000_000) {
          return `${(amount / 1_000_000).toFixed(1)}M`;
        }
        if (amount >= 1000) {
          return `${(amount / 1000).toFixed(0)}k`;
        }
        return amount.toLocaleString();
      };

      return (
        <div className="text-right font-medium">
          {currency || "USD"}
          {formatSalary(salaryMax)}
        </div>
      );
    },
  },
  {
    accessorKey: "salary_currency",
    header: "Currency",
    cell: ({ row }) => {
      const currency = row.getValue("salary_currency") as string;
      return <div className="font-mono text-sm">{currency || "-"}</div>;
    },
  },
  {
    accessorKey: "salary_unit",
    header: "Unit",
    cell: ({ row }) => {
      const unit = row.getValue("salary_unit") as string;
      return <div className="text-sm capitalize">{unit || "-"}</div>;
    },
  },

  // === CAREER & REQUIREMENTS ===
  {
    accessorKey: "career_level",
    header: "Career Level",
    cell: ({ row }) => {
      const levels = row.getValue("career_level") as string[];
      if (!levels || levels.length === 0) {
        return <div className="text-muted-foreground">-</div>;
      }
      return (
        <div className="flex flex-wrap gap-1">
          {levels.slice(0, 2).map((level) => (
            <Badge className="text-xs" key={level} variant="outline">
              {level}
            </Badge>
          ))}
          {levels.length > 2 && (
            <span className="text-muted-foreground text-xs">
              +{levels.length - 2} more
            </span>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "visa_sponsorship",
    header: "Visa",
    cell: ({ row }) => {
      const visa = row.getValue("visa_sponsorship") as string;
      const getVisaVariant = (visaVal: string) => {
        switch (visaVal) {
          case "Yes":
            return "default";
          case "No":
            return "secondary";
          default:
            return "outline";
        }
      };
      if (!visa) {
        return <div className="text-muted-foreground">-</div>;
      }
      return (
        <Badge className="text-xs" variant={getVisaVariant(visa)}>
          {visa}
        </Badge>
      );
    },
  },
  {
    accessorKey: "languages",
    header: "Languages",
    cell: ({ row }) => {
      const languages = row.getValue("languages") as string[];
      if (!languages || languages.length === 0) {
        return <div className="text-muted-foreground">-</div>;
      }
      return (
        <div className="flex flex-wrap gap-1">
          {languages.slice(0, 3).map((lang) => (
            <Badge className="text-xs" key={lang} variant="outline">
              {lang}
            </Badge>
          ))}
          {languages.length > 3 && (
            <span className="text-muted-foreground text-xs">
              +{languages.length - 3} more
            </span>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "skills",
    header: "Skills",
    cell: ({ row }) => {
      const skills = row.getValue("skills") as string;
      return <TruncatedText maxLength={50} text={skills} />;
    },
  },
  {
    accessorKey: "qualifications",
    header: "Qualifications",
    cell: ({ row }) => {
      const qualifications = row.getValue("qualifications") as string;
      return <TruncatedText maxLength={50} text={qualifications} />;
    },
  },
  {
    accessorKey: "education_requirements",
    header: "Education",
    cell: ({ row }) => {
      const education = row.getValue("education_requirements") as string;
      return <TruncatedText maxLength={50} text={education} />;
    },
  },
  {
    accessorKey: "experience_requirements",
    header: "Experience",
    cell: ({ row }) => {
      const experience = row.getValue("experience_requirements") as string;
      return <TruncatedText maxLength={50} text={experience} />;
    },
  },
  {
    accessorKey: "responsibilities",
    header: "Responsibilities",
    cell: ({ row }) => {
      const responsibilities = row.getValue("responsibilities") as string;
      return <TruncatedText maxLength={50} text={responsibilities} />;
    },
  },

  // === BENEFITS & REQUIREMENTS ===
  {
    accessorKey: "benefits",
    header: "Benefits",
    cell: ({ row }) => {
      const benefits = row.getValue("benefits") as string;
      return <TruncatedText maxLength={50} text={benefits} />;
    },
  },
  {
    accessorKey: "application_requirements",
    header: "App Requirements",
    cell: ({ row }) => {
      const requirements = row.getValue("application_requirements") as string;
      return <TruncatedText maxLength={50} text={requirements} />;
    },
  },

  // === DATES ===
  {
    accessorKey: "posted_date",
    header: ({ column }) => {
      return (
        <Button
          className="h-8 px-2"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          variant="ghost"
        >
          <Calendar className="mr-2 h-4 w-4" />
          Posted
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const date = row.getValue("posted_date") as string;
      return (
        <div className="text-muted-foreground text-sm">
          {date ? new Date(date).toLocaleDateString() : "-"}
        </div>
      );
    },
  },
  {
    accessorKey: "valid_through",
    header: ({ column }) => {
      return (
        <Button
          className="h-8 px-2"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          variant="ghost"
        >
          <Calendar className="mr-2 h-4 w-4" />
          Expires
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const date = row.getValue("valid_through") as string;
      if (!date) {
        return <div className="text-muted-foreground">-</div>;
      }

      const expiryDate = new Date(date);
      const now = new Date();
      const isExpired = expiryDate < now;

      return (
        <div
          className={`text-sm ${
            isExpired ? "text-red-600" : "text-muted-foreground"
          }`}
        >
          {expiryDate.toLocaleDateString()}
        </div>
      );
    },
  },
  {
    accessorKey: "sourced_at",
    header: ({ column }) => {
      return (
        <Button
          className="h-8 px-2"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          variant="ghost"
        >
          <Calendar className="mr-2 h-4 w-4" />
          Sourced
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const date = row.getValue("sourced_at") as string;
      return (
        <div className="text-muted-foreground text-sm">
          {date ? new Date(date).toLocaleDateString() : "-"}
        </div>
      );
    },
  },
  {
    accessorKey: "created_at",
    header: ({ column }) => {
      return (
        <Button
          className="h-8 px-2"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          variant="ghost"
        >
          <Calendar className="mr-2 h-4 w-4" />
          Created
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const date = row.getValue("created_at") as string;
      return (
        <div className="text-muted-foreground text-sm">
          {new Date(date).toLocaleDateString()}
        </div>
      );
    },
  },
  {
    accessorKey: "updated_at",
    header: ({ column }) => {
      return (
        <Button
          className="h-8 px-2"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          variant="ghost"
        >
          <Calendar className="mr-2 h-4 w-4" />
          Updated
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const date = row.getValue("updated_at") as string;
      return (
        <div className="text-muted-foreground text-sm">
          {new Date(date).toLocaleDateString()}
        </div>
      );
    },
  },
  {
    accessorKey: "last_checked_at",
    header: "Last Checked",
    cell: ({ row }) => {
      const date = row.getValue("last_checked_at") as string;
      return (
        <div className="text-muted-foreground text-sm">
          {date ? new Date(date).toLocaleDateString() : "-"}
        </div>
      );
    },
  },
  {
    accessorKey: "raw_data_fetched_at",
    header: "Data Fetched",
    cell: ({ row }) => {
      const date = row.getValue("raw_data_fetched_at") as string;
      return (
        <div className="text-muted-foreground text-sm">
          {date ? new Date(date).toLocaleDateString() : "-"}
        </div>
      );
    },
  },
  {
    accessorKey: "next_try_at",
    header: "Next Try",
    cell: ({ row }) => {
      const date = row.getValue("next_try_at") as string;
      return (
        <div className="text-muted-foreground text-sm">
          {date ? new Date(date).toLocaleDateString() : "-"}
        </div>
      );
    },
  },
  {
    accessorKey: "deleted_at",
    header: "Deleted",
    cell: ({ row }) => {
      const date = row.getValue("deleted_at") as string;
      if (!date) {
        return <div className="text-muted-foreground">-</div>;
      }

      return (
        <div className="text-red-600 text-sm">
          {new Date(date).toLocaleDateString()}
        </div>
      );
    },
  },

  // === IDENTIFIERS & METADATA ===
  {
    accessorKey: "job_identifier",
    header: "Job ID",
    cell: ({ row }) => {
      const jobId = row.getValue("job_identifier") as string;
      return <div className="font-mono text-sm">{jobId || "-"}</div>;
    },
  },
  {
    accessorKey: "job_source_name",
    header: "Source",
    cell: ({ row }) => {
      const source = row.getValue("job_source_name") as string;
      return <div className="text-sm">{source || "-"}</div>;
    },
  },
  {
    accessorKey: "source_type",
    header: "Source Type",
    cell: ({ row }) => {
      const sourceType = row.getValue("source_type") as string;
      return <div className="text-sm capitalize">{sourceType || "-"}</div>;
    },
  },
  {
    accessorKey: "source_name",
    header: "Source Name",
    cell: ({ row }) => {
      const sourceName = row.getValue("source_name") as string;
      return <div className="text-sm">{sourceName || "-"}</div>;
    },
  },
  {
    accessorKey: "dedupe_key",
    header: "Dedupe Key",
    cell: ({ row }) => {
      const key = row.getValue("dedupe_key") as string;
      if (!key) {
        return <div className="text-muted-foreground">-</div>;
      }

      return (
        <div className="font-mono text-sm" title={key}>
          {key.slice(0, 20)}...
        </div>
      );
    },
  },

  // === SYSTEM MONITORING ===
  {
    accessorKey: "monitor_attempts",
    header: "Monitor Attempts",
    cell: ({ row }) => {
      const attempts = row.getValue("monitor_attempts") as number;
      return <div className="text-center text-sm">{attempts || 0}</div>;
    },
  },

  // === AIRTABLE INTEGRATION ===
  {
    accessorKey: "airtable_synced_at",
    header: "Airtable Synced",
    cell: ({ row }) => {
      const date = row.getValue("airtable_synced_at") as string;
      return (
        <div className="text-muted-foreground text-sm">
          {date ? new Date(date).toLocaleDateString() : "-"}
        </div>
      );
    },
  },
  {
    accessorKey: "airtable_id",
    header: "Airtable ID",
    cell: ({ row }) => {
      const id = row.getValue("airtable_id") as string;
      return <div className="font-mono text-sm">{id || "-"}</div>;
    },
  },

  // === JOB BOARDS INTEGRATION ===
  {
    accessorKey: "posted_to_boards",
    header: "Posted Boards",
    cell: ({ row }) => {
      // biome-ignore lint/suspicious/noExplicitAny: JSONB field can contain any structure (array, object, null)
      const boards = row.getValue("posted_to_boards") as any;
      // Handle JSONB field safely - could be null, array, or other structure
      if (!(boards && Array.isArray(boards)) || boards.length === 0) {
        return <div className="text-muted-foreground">-</div>;
      }

      // Ensure boards are strings for display
      const validBoards = boards
        .filter(
          (board) => typeof board === "string" || typeof board === "number"
        )
        .map((board) => String(board));

      if (validBoards.length === 0) {
        return <div className="text-muted-foreground">-</div>;
      }

      return (
        <div className="flex flex-wrap gap-1">
          {validBoards.slice(0, 2).map((board) => (
            <Badge className="text-xs" key={board} variant="outline">
              {board}
            </Badge>
          ))}
          {validBoards.length > 2 && (
            <Badge className="text-xs" variant="outline">
              +{validBoards.length - 2}
            </Badge>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "last_posted_at",
    header: "Last Posted",
    cell: ({ row }) => {
      const date = row.getValue("last_posted_at") as string;
      return (
        <div className="text-muted-foreground text-sm">
          {date ? new Date(date).toLocaleDateString() : "-"}
        </div>
      );
    },
  },
  {
    accessorKey: "source_id",
    header: "Source ID",
    cell: ({ row }) => {
      const sourceId = row.getValue("source_id") as string;
      return <div className="font-mono text-sm">{sourceId || "-"}</div>;
    },
  },

  // === TECHNICAL DATA ===
  {
    accessorKey: "ai_metadata",
    header: "AI Metadata",
    cell: ({ row }) => {
      // biome-ignore lint/suspicious/noExplicitAny: AI metadata can be any JSON structure from processing
      const metadata = row.getValue("ai_metadata") as any;
      return (
        <div
          className="max-w-[200px] truncate font-mono text-muted-foreground text-sm"
          title={formatJsonField(metadata)}
        >
          {formatJsonField(metadata)}
        </div>
      );
    },
  },
  {
    accessorKey: "raw_sourced_job_data",
    header: "Raw Data",
    cell: ({ row }) => {
      // biome-ignore lint/suspicious/noExplicitAny: Raw job data can be any structure from various sources
      const rawData = row.getValue("raw_sourced_job_data") as any;
      return (
        <div
          className="max-w-[200px] truncate font-mono text-muted-foreground text-sm"
          title={formatJsonField(rawData)}
        >
          {formatJsonField(rawData)}
        </div>
      );
    },
  },

  // === MISSING FIELDS FROM DATABASE ===
  {
    accessorKey: "tags",
    header: "Tags",
    cell: ({ row }) => {
      // biome-ignore lint/suspicious/noExplicitAny: JSONB field can contain any structure (array, object, null)
      const tags = row.getValue("tags") as any;
      // Handle JSONB field safely - could be null, array, or other structure
      if (!(tags && Array.isArray(tags)) || tags.length === 0) {
        return <div className="text-muted-foreground">-</div>;
      }

      // Ensure tags are strings for display
      const validTags = tags
        .filter((tag) => typeof tag === "string" || typeof tag === "number")
        .map((tag) => String(tag));

      if (validTags.length === 0) {
        return <div className="text-muted-foreground">-</div>;
      }

      return (
        <div className="flex flex-wrap gap-1">
          {validTags.slice(0, 3).map((tag) => (
            <Badge className="text-xs" key={tag} variant="outline">
              {tag}
            </Badge>
          ))}
          {validTags.length > 3 && (
            <Badge className="text-xs" variant="outline">
              +{validTags.length - 3}
            </Badge>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "apply_email",
    header: "Apply Email",
    cell: ({ row }) => {
      const email = row.getValue("apply_email") as string;
      if (!email) {
        return <div className="text-muted-foreground">-</div>;
      }

      return (
        <div className="flex items-center">
          <Mail className="mr-2 h-4 w-4 text-muted-foreground" />
          <a
            className="max-w-[200px] truncate text-blue-600 hover:underline"
            href={`mailto:${email}`}
            onClick={(e) => e.stopPropagation()}
            title={email}
          >
            {email}
          </a>
        </div>
      );
    },
  },
  {
    accessorKey: "external_id",
    header: "External ID",
    cell: ({ row }) => {
      const externalId = row.getValue("external_id") as string;
      if (!externalId) {
        return <div className="text-muted-foreground">-</div>;
      }

      return (
        <div className="font-mono text-sm" title={externalId}>
          {externalId.length > 20
            ? `${externalId.slice(0, 20)}...`
            : externalId}
        </div>
      );
    },
  },
  {
    accessorKey: "processed_at",
    header: ({ column }) => {
      return (
        <Button
          className="h-8 px-2"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          variant="ghost"
        >
          <Calendar className="mr-2 h-4 w-4" />
          Processed
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const date = row.getValue("processed_at") as string;
      return (
        <div className="text-muted-foreground text-sm">
          {date ? new Date(date).toLocaleDateString() : "-"}
        </div>
      );
    },
  },

  // === ACTIONS ===
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const job = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button className="h-8 w-8 p-0" variant="ghost">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(job.id)}
            >
              Copy job ID
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <a
                className="flex items-center"
                href={`/dashboard/jobs/${job.id}`}
              >
                <ExternalLink className="mr-2 h-4 w-4" />
                View details
              </a>
            </DropdownMenuItem>
            {job.apply_url && (
              <DropdownMenuItem asChild>
                <a
                  className="flex items-center"
                  href={job.apply_url}
                  rel="noopener noreferrer"
                  target="_blank"
                >
                  <ExternalLink className="mr-2 h-4 w-4" />
                  View job posting
                </a>
              </DropdownMenuItem>
            )}
            {job.source_url && job.source_url !== job.apply_url && (
              <DropdownMenuItem asChild>
                <a
                  className="flex items-center"
                  href={job.source_url}
                  rel="noopener noreferrer"
                  target="_blank"
                >
                  <Globe className="mr-2 h-4 w-4" />
                  View source
                </a>
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
